import {
    EuiBasicTable,
    EuiButton,
    EuiFieldSearch,
    EuiFlexGroup,
    EuiFlexItem,
    EuiPanel,
    EuiSelect,
    EuiSpacer,
    EuiText,
    EuiTitle,
    EuiIcon,
    EuiDragDropContext,
    EuiDroppable,
    EuiDraggable,
    euiDragDropReorder,
    htmlIdGenerator,
} from '@elastic/eui';
import React, { useState } from 'react';
import { ActionCell, ImageCell, MetricCell } from '@/curator/components/table/cells';

interface Product {
    id: string;
    image: string;
    name: string;
    conversion: number;
    margin: number;
    dhf: number;
    volume: number;
}

interface ThemeProductsTabProps {
    onNext?: () => void;
    themeId?: string;
}

// Dummy data for EDIT mode
const DUMMY_COLLECTION_PRODUCTS: Product[] = [
    {
        id: 'P001',
        image: "https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg",
        name: 'Organic Bananas',
        conversion: 12.5,
        margin: 25.3,
        dhf: 8.2,
        volume: 1250,
    },
    {
        id: 'P002',
        image: "https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg",
        name: 'Fresh Strawberries',
        conversion: 8.7,
        margin: 18.9,
        dhf: 6.1,
        volume: 890,
    },
];

const DUMMY_SEARCH_PRODUCTS: Product[] = [
    {
        id: 'P003',
        image: "https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg",
        name: 'Avocados',
        conversion: 15.2,
        margin: 22.1,
        dhf: 9.3,
        volume: 750,
    },
    {
        id: 'P004',
        image: "https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg",
        name: 'Blueberries',
        conversion: 11.8,
        margin: 28.5,
        dhf: 7.9,
        volume: 650,
    },
];

const DUMMY_RECOMMENDATIONS: Product[] = [
    {
        id: 'P005',
        image: "https://storage.cloud.google.com/nube-piscina-prod-no-digital-product-assets/product_images/00000020548735_NO_17471784.jpeg",
        name: 'Mangoes',
        conversion: 9.4,
        margin: 31.2,
        dhf: 5.8,
        volume: 420,
    },
];

const ThemeProductsTab: React.FC<ThemeProductsTabProps> = ({ onNext, themeId: _ }) => {
    const [collectionProducts, setCollectionProducts] = useState(DUMMY_COLLECTION_PRODUCTS);
    const [searchProducts] = useState(DUMMY_SEARCH_PRODUCTS);
    const [recommendations] = useState(DUMMY_RECOMMENDATIONS);

    // Pagination for search only
    const [searchPagination, setSearchPagination] = useState({
        pageIndex: 0,
        pageSize: 10,
        totalItemCount: DUMMY_SEARCH_PRODUCTS.length,
        pageSizeOptions: [10, 25, 50],
    });

    const onDragEnd = ({ source, destination }: any) => {
        if (source && destination) {
            const items = euiDragDropReorder(collectionProducts, source.index, destination.index);
            setCollectionProducts(items);
        }
    };

    const handleProductAction = (action: string, productId: string) => {
        console.log(`${action} product:`, productId);
        if (action === 'remove') {
            setCollectionProducts(prev => prev.filter(p => p.id !== productId));
        } else if (action === 'add') {
            // Add product to collection from search/recommendations
            const sourceProduct = [...searchProducts, ...recommendations].find(p => p.id === productId);
            if (sourceProduct && !collectionProducts.find(p => p.id === productId)) {
                setCollectionProducts(prev => [...prev, sourceProduct]);
            }
        }
    };

    const handleSearchPageChange = ({ page }: { page?: { index: number; size: number } }) => {
        if (page) {
            setSearchPagination(prev => ({
                ...prev,
                pageIndex: page.index,
                pageSize: page.size,
            }));
        }
    };

    // Column definitions for search and recommendations tables
    const getSearchColumns = () => [
      
        {
            field: 'id',
            name: 'Product ID',
            width: '100px',
        },
          {
            field: 'image',
            name: '',
            render: (image: string) => <ImageCell src={image} size="s" data={{}} alt="" />,
            width: '60px',
        },
        {
            field: 'name',
            name: 'Product name',
        },
        {
            field: 'conversion',
            name: 'Conversion',
            render: (value: number) => <MetricCell data={{}} value={value} suffix=" %" />,
        },
        {
            field: 'margin',
            name: 'Margin',
            render: (value: number) => <MetricCell data={{}} value={value} suffix="%" />,
        },
        {
            field: 'dhf',
            name: 'DHF',
            render: (value: number) => <MetricCell data={{}} value={value} />,
        },
        {
            field: 'volume',
            name: 'Volume',
            render: (value: number) => <MetricCell data={{}} value={value} />,
        },
        {
            field: 'actions',
            name: 'Actions',
            render: (_: any, product: Product) => (
                <ActionCell
                    data={product}
                    actions={[
                        {
                            id: 'add',
                            label: 'Add',
                            iconType: 'plus',
                            onClick: () => handleProductAction('add', product.id),
                        },
                    ]}
                />
            ),
        },
    ];

    return (
        <>
            <EuiFlexGroup direction="column">
                {/* Collection Section - Drag & Drop Table */}
                <EuiFlexItem>
                    
                        <EuiTitle size="s">
                            <h3>Collection</h3>
                        </EuiTitle>
                        <EuiSpacer size="m" />

                        {/* Collection Table Header */}
                        <div style={{
                            backgroundColor: '#F5F7FA',
                            padding: '12px 16px',
                            fontWeight: 600,
                            fontSize: '14px',
                            color: '#343741',
                            marginBottom: '8px',
                            border: '1px solid #D3DAE6',
                            borderRadius: '6px 6px 0 0'
                        }}>
                            <EuiFlexGroup gutterSize="s" alignItems="center">
                                <EuiFlexItem grow={false} style={{ width: 40 }} />
                                <EuiFlexItem grow={false} style={{ width: 100 }}>Product ID</EuiFlexItem>
                                <EuiFlexItem grow={false} style={{ width: 60 }}></EuiFlexItem>
                                <EuiFlexItem>Product name</EuiFlexItem>
                                <EuiFlexItem>Conversion</EuiFlexItem>
                                <EuiFlexItem>Margin</EuiFlexItem>
                                <EuiFlexItem>DHF</EuiFlexItem>
                                <EuiFlexItem>Volume</EuiFlexItem>
                                <EuiFlexItem>Actions</EuiFlexItem>
                            </EuiFlexGroup>
                        </div>

                        {/* Collection Table Body - Drag & Drop */}
                        <div style={{
                           // border: '1px solid #D3DAE6',
                            borderTop: 'none',
                            borderRadius: '0 0 6px 6px',
                            overflow: 'hidden'
                        }}>
                            <EuiDragDropContext onDragEnd={onDragEnd}>
                                <EuiDroppable
                                    droppableId="COLLECTION_PRODUCTS"
                                    spacing="none"
                                    withPanel={false}
                                >
                                    {collectionProducts.map((product, idx) => (
                                        <EuiDraggable
                                            spacing="none"
                                            key={product.id}
                                            index={idx}
                                            draggableId={product.id}
                                            customDragHandle={true}
                                            hasInteractiveChildren={true}
                                        >
                                            {(provided, snapshot) => (
                                                <EuiPanel
                                                    paddingSize="s"
                                                    hasShadow={false}
                                                    style={{
                                                        margin: '0 0 1px 0',
                                                        borderRadius: 0,
                                                        borderBottom: '1px solid #d3dae6',
                                                        boxShadow: 'none'
                                                    }}
                                                >
                                                    <EuiFlexGroup alignItems="center" gutterSize="s">
                                                        <EuiFlexItem grow={false} style={{ width: 40 }}>
                                                            <div
                                                                {...provided.dragHandleProps}
                                                                style={{
                                                                    cursor: 'grab',
                                                                    padding: '4px',
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                }}
                                                            >
                                                                <EuiIcon type="grab" />
                                                            </div>
                                                        </EuiFlexItem>

                                                        <EuiFlexItem grow={false} style={{ width: 100 }}>
                                                            <EuiText size="s">{product.id}</EuiText>
                                                        </EuiFlexItem>

                                                        <EuiFlexItem grow={false} style={{ width: 60 }}>
                                                            <ImageCell src={product.image} size="s" data={{}} alt="" />
                                                        </EuiFlexItem>

                                                        <EuiFlexItem>
                                                            <EuiText size="s">{product.name}</EuiText>
                                                        </EuiFlexItem>

                                                        <EuiFlexItem>
                                                            <MetricCell data={{}} value={product.conversion} suffix=" %" />
                                                        </EuiFlexItem>

                                                        <EuiFlexItem>
                                                            <MetricCell data={{}} value={product.margin} suffix="%" />
                                                        </EuiFlexItem>

                                                        <EuiFlexItem>
                                                            <MetricCell data={{}} value={product.dhf} />
                                                        </EuiFlexItem>

                                                        <EuiFlexItem>
                                                            <MetricCell data={{}} value={product.volume} />
                                                        </EuiFlexItem>

                                                        <EuiFlexItem grow={false} style={{ width: 100 }}>
                                                            <ActionCell
                                                                item={product}
                                                                actions={[
                                                                    {
                                                                        id: 'pin',
                                                                        label: 'Pin',
                                                                        iconType: 'pin',
                                                                        onClick: () => handleProductAction('pin', product.id),
                                                                    },
                                                                    {
                                                                        id: 'remove',
                                                                        label: 'Remove',
                                                                        iconType: 'cross',
                                                                        onClick: () => handleProductAction('remove', product.id),
                                                                    },
                                                                ]}
                                                            />
                                                        </EuiFlexItem>
                                                    </EuiFlexGroup>
                                                </EuiPanel>
                                            )}
                                        </EuiDraggable>
                                    ))}
                                </EuiDroppable>
                            </EuiDragDropContext>
                        </div>
                    
                </EuiFlexItem>

                {/* Find Products Section - With Search & Pagination */}
                <EuiFlexItem>
                    
                        <EuiTitle size="s">
                            <h3>Find products</h3>
                        </EuiTitle>
                        <EuiSpacer size="m" />

                        {/* Search Controls */}
                        <EuiFlexGroup gutterSize="m">
                            <EuiFlexItem>
                                <EuiSelect
                                    options={[
                                        { value: 'all', text: 'Categories' },
                                        { value: 'category1', text: 'Category 1' },
                                        { value: 'category2', text: 'Category 2' },
                                    ]}
                                />
                            </EuiFlexItem>
                            <EuiFlexItem grow={2}>
                                <EuiFieldSearch placeholder="Search" />
                            </EuiFlexItem>
                        </EuiFlexGroup>

                        <EuiSpacer size="m" />

                        {/* Search Results Table with Pagination */}
                        <EuiBasicTable
                            items={searchProducts}
                            columns={getSearchColumns()}
                            pagination={searchPagination}
                            onChange={handleSearchPageChange}
                        />
                
                </EuiFlexItem>

                {/* Recommendations Section - No Pagination */}
                <EuiFlexItem>
                    
                        <EuiTitle size="s">
                            <h3>Recommended</h3>
                        </EuiTitle>
                        <EuiText size="s" color="subdued">
                            Based on recipes and added products
                        </EuiText>
                        <EuiSpacer size="m" />

                        {/* Simple Table - No Pagination */}
                        <EuiBasicTable
                            items={recommendations}
                            columns={getSearchColumns()}
                        />
                
                </EuiFlexItem>
            </EuiFlexGroup>

            <EuiSpacer size="l" />

            <EuiButton iconType="arrowRight" iconSide="right" onClick={onNext}>
                Continue to Performance
            </EuiButton>
        </>
    );
};

export default ThemeProductsTab;
