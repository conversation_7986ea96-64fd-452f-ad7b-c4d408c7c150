import {
    EuiBasicTable,
    <PERSON><PERSON>S<PERSON><PERSON>,
    <PERSON>ui<PERSON><PERSON><PERSON>,
    EuiTitle,
} from '@elastic/eui';
import React from 'react';

interface Product {
    id: string;
    image: string;
    name: string;
    conversion: number;
    margin: number;
    dhf: number;
    volume: number;
    isPinned?: boolean;
}

interface ProductRecommendationProps {
    recommendations: Product[];
    getSearchColumns: () => any[];
}

const ProductRecommendation: React.FC<ProductRecommendationProps> = ({
    recommendations,
    getSearchColumns,
}) => {
    return (
        <>
            <EuiTitle size="s">
                <h3>Recommended</h3>
            </EuiTitle>
            <EuiText size="s" color="subdued">
                Based on recipes and added products
            </EuiText>
            <EuiSpacer size="m" />

            {/* Simple Table - No Pagination */}
            <EuiBasicTable
                items={recommendations}
                columns={getSearchColumns()}
            />
        </>
    );
};

export default ProductRecommendation;
