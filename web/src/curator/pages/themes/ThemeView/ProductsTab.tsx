import {
    EuiBasicTable,
    EuiFieldSearch,
    EuiFlexGroup,
    EuiFlexItem,
    EuiPanel,
    EuiSelect,
    EuiSpacer,
    EuiText,
    EuiTitle,
    EuiIcon,
    EuiButtonIcon,
    EuiDragDropContext,
    EuiDroppable,
    EuiDraggable,
    euiDragDropReorder,
    htmlIdGenerator,
} from '@elastic/eui';
import React, { useState } from 'react';
import { ActionCell, ImageCell, MetricCell, BadgeCell } from '@/curator/components/table/cells';

interface Product {
    id: string;
    image: string;
    name: string;
    conversion: number;
    margin: number;
    dhf: number;
    volume: number;
    ctr?: number;
    impressions?: number;
    gmiClick?: string;
    isPinned?: boolean;
}

interface ProductsTabProps {
    mode: 'edit' | 'view';
    themeId: string;
}

// Dummy data
const DUMMY_COLLECTION_PRODUCTS: Product[] = [
    {
        id: 'P001',
        image: '/api/placeholder/40/40',
        name: 'Organic Bananas',
        conversion: 12.5,
        margin: 25.3,
        dhf: 8.2,
        volume: 1250,
        ctr: 3.2,
        impressions: 15000,
        gmiClick: '$2.45',
        isPinned: true,
    },
    {
        id: 'P002',
        image: '/api/placeholder/40/40',
        name: 'Fresh Strawberries',
        conversion: 8.7,
        margin: 18.9,
        dhf: 6.1,
        volume: 890,
        ctr: 2.8,
        impressions: 12000,
        gmiClick: '$3.12',
        isPinned: false,
    },
];

const DUMMY_SEARCH_PRODUCTS: Product[] = [
    {
        id: 'P003',
        image: '/api/placeholder/40/40',
        name: 'Avocados',
        conversion: 15.2,
        margin: 22.1,
        dhf: 9.3,
        volume: 750,
    },
];

const DUMMY_RECOMMENDATIONS: Product[] = [
    {
        id: 'P004',
        image: '/api/placeholder/40/40',
        name: 'Blueberries',
        conversion: 11.8,
        margin: 28.5,
        dhf: 7.9,
        volume: 650,
    },
];

const ProductsTab: React.FC<ProductsTabProps> = ({ mode, themeId: _ }) => {
    const [collectionProducts, setCollectionProducts] = useState(DUMMY_COLLECTION_PRODUCTS);
    const [searchProducts] = useState(DUMMY_SEARCH_PRODUCTS);
    const [recommendations] = useState(DUMMY_RECOMMENDATIONS);

    // Pagination for search only
    const [searchPagination, setSearchPagination] = useState({
        pageIndex: 0,
        pageSize: 10,
        totalItemCount: DUMMY_SEARCH_PRODUCTS.length,
        pageSizeOptions: [10, 25, 50],
    });

    const onDragEnd = ({ source, destination }: any) => {
        if (source && destination) {
            const items = euiDragDropReorder(collectionProducts, source.index, destination.index);
            setCollectionProducts(items);
        }
    };

    const handleProductAction = (action: string, productId: string) => {
        console.log(`${action} product:`, productId);
        if (action === 'remove') {
            setCollectionProducts(prev => prev.filter(p => p.id !== productId));
        }
    };

    const handleSearchPageChange = ({ page }: { page?: { index: number; size: number } }) => {
        if (page) {
            setSearchPagination(prev => ({
                ...prev,
                pageIndex: page.index,
                pageSize: page.size,
            }));
        }
    };

    return (
        <div data-test-subj="products-tab">
            <EuiFlexGroup direction="column">
                {/* Collection Section - Drag & Drop Table */}
                <EuiFlexItem>
                    <EuiPanel>
                        <EuiTitle size="s">
                            <h3>Collection</h3>
                        </EuiTitle>
                        <EuiSpacer size="m" />

                        {/* Collection Table Header */}
                        <div style={{
                            backgroundColor: '#F5F7FA',
                            padding: '12px 16px',
                            fontWeight: 600,
                            fontSize: '14px',
                            color: '#343741',
                            marginBottom: '8px',
                            border: '1px solid #D3DAE6',
                            borderRadius: '6px 6px 0 0'
                        }}>
                            <EuiFlexGroup gutterSize="m" alignItems="center">
                                <EuiFlexItem grow={false} style={{ width: 40 }} />
                                <EuiFlexItem grow={false} style={{ width: 60 }}>Image</EuiFlexItem>
                                <EuiFlexItem grow={false} style={{ width: 100 }}>Product ID</EuiFlexItem>
                                <EuiFlexItem style={{ minWidth: 200 }}>Product Name</EuiFlexItem>
                                <EuiFlexItem grow={false} style={{ width: 100 }}>
                                    {mode === 'edit' ? 'Conversion ↓' : 'CTR ↓'}
                                </EuiFlexItem>
                                <EuiFlexItem grow={false} style={{ width: 100 }}>
                                    {mode === 'edit' ? 'Margin' : 'Impressions'}
                                </EuiFlexItem>
                                <EuiFlexItem grow={false} style={{ width: 100 }}>
                                    {mode === 'edit' ? 'DHF' : 'GMI/click'}
                                </EuiFlexItem>
                                <EuiFlexItem grow={false} style={{ width: 100 }}>Volume</EuiFlexItem>
                                <EuiFlexItem grow={false} style={{ width: 100 }}>
                                    {mode === 'edit' ? 'Actions' : 'Pinned'}
                                </EuiFlexItem>
                            </EuiFlexGroup>
                        </div>

                        {/* Collection Table Body - Drag & Drop */}
                        <div style={{
                            border: '1px solid #D3DAE6',
                            borderTop: 'none',
                            borderRadius: '0 0 6px 6px',
                            overflow: 'hidden'
                        }}>
                            <EuiDragDropContext onDragEnd={onDragEnd}>
                                <EuiDroppable
                                    droppableId="COLLECTION_PRODUCTS"
                                    spacing="none"
                                    withPanel={false}
                                >
                                    {collectionProducts.map((product, idx) => (
                                        <EuiDraggable
                                            spacing="none"
                                            key={product.id}
                                            index={idx}
                                            draggableId={product.id}
                                            customDragHandle={true}
                                            hasInteractiveChildren={true}
                                        >
                                            {(provided, snapshot) => (
                                                <div
                                                    ref={provided.innerRef}
                                                    {...provided.draggableProps}
                                                    style={{
                                                        ...provided.draggableProps.style,
                                                        backgroundColor: snapshot.isDragging ? '#F5F7FA' : 'white',
                                                        borderBottom: idx < collectionProducts.length - 1 ? '1px solid #D3DAE6' : 'none',
                                                        padding: '12px 16px',
                                                    }}
                                                >
                                                    <EuiFlexGroup alignItems="center" gutterSize="m">
                                                        <EuiFlexItem grow={false} style={{ width: 40 }}>
                                                            <div
                                                                {...provided.dragHandleProps}
                                                                style={{
                                                                    cursor: 'grab',
                                                                    padding: '4px',
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                }}
                                                            >
                                                                <EuiIcon type="grab" />
                                                            </div>
                                                        </EuiFlexItem>

                                                        <EuiFlexItem grow={false} style={{ width: 60 }}>
                                                            <ImageCell src={product.image} size="s" />
                                                        </EuiFlexItem>

                                                        <EuiFlexItem grow={false} style={{ width: 100 }}>
                                                            <EuiText size="s">{product.id}</EuiText>
                                                        </EuiFlexItem>

                                                        <EuiFlexItem style={{ minWidth: 200 }}>
                                                            <EuiText size="s" style={{ fontWeight: 600 }}>
                                                                {product.name}
                                                            </EuiText>
                                                        </EuiFlexItem>

                                                        <EuiFlexItem grow={false} style={{ width: 100 }}>
                                                            <MetricCell
                                                                data={{}}
                                                                value={mode === 'edit' ? product.conversion : product.ctr || 0}
                                                                suffix=" %"
                                                            />
                                                        </EuiFlexItem>

                                                        <EuiFlexItem grow={false} style={{ width: 100 }}>
                                                            {mode === 'edit' ? (
                                                                <MetricCell data={{}} value={product.margin} suffix="%" />
                                                            ) : (
                                                                <MetricCell data={{}} value={product.impressions || 0} />
                                                            )}
                                                        </EuiFlexItem>

                                                        <EuiFlexItem grow={false} style={{ width: 100 }}>
                                                            {mode === 'edit' ? (
                                                                <MetricCell data={{}} value={product.dhf} />
                                                            ) : (
                                                                <MetricCell data={{}} value={product.gmiClick || ''} />
                                                            )}
                                                        </EuiFlexItem>

                                                        <EuiFlexItem grow={false} style={{ width: 100 }}>
                                                            <MetricCell data={{}} value={product.volume} />
                                                        </EuiFlexItem>

                                                        <EuiFlexItem grow={false} style={{ width: 100 }}>
                                                            {mode === 'edit' ? (
                                                                <ActionCell
                                                                    data={product}
                                                                    actions={[
                                                                        {
                                                                            id: 'pin',
                                                                            label: 'Pin',
                                                                            iconType: 'pin',
                                                                            onClick: () => handleProductAction('pin', product.id),
                                                                        },
                                                                        {
                                                                            id: 'remove',
                                                                            label: 'Remove',
                                                                            iconType: 'cross',
                                                                            onClick: () => handleProductAction('remove', product.id),
                                                                        },
                                                                    ]}
                                                                />
                                                            ) : (
                                                                <BadgeCell data={{}} text={product.isPinned ? '📌' : ''} />
                                                            )}
                                                        </EuiFlexItem>
                                                    </EuiFlexGroup>
                                                </div>
                                            )}
                                        </EuiDraggable>
                                    ))}
                                </EuiDroppable>
                            </EuiDragDropContext>
                        </div>
                    </EuiPanel>
                </EuiFlexItem>